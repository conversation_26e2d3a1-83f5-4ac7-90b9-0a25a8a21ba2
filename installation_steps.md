# HaCasa 房間卡片安裝步驟

## 第一步：安裝必要組件

### 1. 安裝 Button Card
1. 打開 Home Assistant
2. 進入 HACS (Home Assistant Community Store)
3. 點擊 "Frontend"
4. 搜索 "Button Card"
5. 點擊安裝
6. 重啟 Home Assistant

### 2. 下載 HaCasa 主題
1. 訪問 https://github.com/dam<PERSON><PERSON><PERSON><PERSON>/HaCasa
2. 點擊 "Code" -> "Download ZIP"
3. 解壓縮文件

## 第二步：文件配置

### 1. 創建目錄結構
在您的 Home Assistant 配置目錄中創建以下結構：
```
config/
├── themes/
│   └── HaCasa/
├── dashboard/
└── configuration.yaml
```

### 2. 複製主題文件
1. 將下載的 HaCasa 主題文件複製到 `config/themes/HaCasa/` 目錄
2. 或者使用我提供的 `themes/HaCasa/hacasa.yaml` 文件

### 3. 複製儀表板配置
1. 將 `dashboard/rooms.yaml` 文件放入 `config/dashboard/` 目錄

## 第三步：配置 Home Assistant

### 1. 修改 configuration.yaml
將以下內容添加到您的 `configuration.yaml` 文件：

```yaml
# 主題配置
frontend:
  themes: !include_dir_merge_named themes

# Lovelace配置
lovelace:
  mode: storage
  resources:
    - url: /hacsfiles/button-card/button-card.js
      type: module
    - url: "https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900"
      type: css
  dashboards:
    ha-casa-rooms:
      mode: yaml
      title: HaCasa Rooms
      icon: mdi:home
      show_in_sidebar: true
      filename: dashboard/rooms.yaml
```

### 2. 添加實體配置
將 `example_entities.yaml` 中的內容添加到您的配置中，或根據您的實際設備進行調整。

## 第四步：重啟和測試

### 1. 重啟 Home Assistant
1. 進入 "設定" -> "系統" -> "重新啟動"
2. 等待重啟完成

### 2. 選擇主題
1. 進入 "設定" -> "外觀"
2. 在 "主題" 下拉選單中選擇 "HaCasa"

### 3. 查看儀表板
1. 在側邊欄中找到 "HaCasa Rooms"
2. 點擊進入查看房間卡片

## 第五步：自定義配置

### 1. 調整實體
根據您的實際設備，修改 `dashboard/rooms.yaml` 中的實體名稱：
- `sensor.living_room_temperature` -> 您的溫度傳感器
- `switch.living_room_fan` -> 您的風扇開關
- 等等...

### 2. 修改圖標和顏色
在 `dashboard/rooms.yaml` 中的 `variables` 部分調整：
- `room_icon`: 房間圖標
- `icon_color`: 圖標背景顏色
- `control1_icon` 等: 控制按鈕圖標

### 3. 添加更多房間
複製現有的房間卡片配置，修改相關參數即可添加新房間。

## 故障排除

### 常見問題
1. **卡片不顯示**: 確認 Button Card 已正確安裝
2. **主題不生效**: 檢查 themes 目錄結構和 configuration.yaml 配置
3. **實體錯誤**: 確認實體名稱正確且存在

### 檢查日誌
在 "設定" -> "系統" -> "日誌" 中查看錯誤信息。

## 進階自定義

### 1. 添加動畫效果
在卡片樣式中添加 CSS 動畫：
```yaml
styles:
  card:
    - transition: all 0.3s ease
    - transform: scale(1)
    - '&:hover': 
        transform: scale(1.02)
```

### 2. 響應式設計
根據螢幕大小調整卡片佈局：
```yaml
styles:
  card:
    - '@media (max-width: 768px)':
        - height: 150px
        - padding: 15px
```

### 3. 自定義顏色主題
修改 `themes/HaCasa/hacasa.yaml` 中的顏色變數來創建您自己的配色方案。
