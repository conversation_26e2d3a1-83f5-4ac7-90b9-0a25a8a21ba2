esphome:
  name: sp2500
  friendly_name: sp2500

esp32:
  board: m5stack-atom
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG

# Enable Home Assistant API
api:

ota:
  - platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "Sp2500 Fallback Hotspot"
    password: "KUpOksaTIe2I"

captive_portal:

uart:
  rx_pin: 22
  tx_pin: 19
  baud_rate: 9600  # 嘗試其他波特率：4800, 19200
  data_bits: 8
  stop_bits: 1
  rx_buffer_size: 512  # 增加接收緩衝區大小

modbus:
  send_wait_time: 500ms  # 增加等待時間
  id: modbus_pv

# Modbus 控制器 (從機地址 1)
modbus_controller:
  - id: sp2500
    address: 1  # 從機地址，預設 0x01
    modbus_id: modbus_pv
    update_interval: 10s  # 增加輪詢間隔
    setup_priority: -10
    command_throttle: 500ms  # 增加命令節流時間

sensor:
  # Grid voltage (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Grid Voltage"
    id: grid_voltage
    icon: mdi:sine-wave
    register_type: holding  # 嘗試 input 如果 holding 不工作
    address: 0xD1  # 簡化地址格式
    unit_of_measurement: "V"
    timeout: 5s  # 增加超時時間
    skip_updates: 3  # 允許跳過幾次更新
    filters:
      - filter_out: 0xFFFF  # 過濾無效值

  # AC output voltage (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Voltage"
    id: ac_output_voltage
    icon: mdi:sine-wave
    register_type: holding
    address: 0xD8
    unit_of_measurement: "V"
    timeout: 5s
    skip_updates: 3
    filters:
      - filter_out: 0xFFFF

  # AC output load percent (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Load Percent"
    id: ac_output_load_percent
    icon: mdi:percent
    register_type: holding
    address: 0xDD
    unit_of_measurement: "%"
    timeout: 5s
    skip_updates: 3
    filters:
      - filter_out: 0xFFFF

  # Battery capacity (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Battery Capacity"
    id: battery_capacity
    icon: mdi:battery-charging-high
    register_type: holding
    address: 0xE2
    unit_of_measurement: "%"
    timeout: 5s
    skip_updates: 3
    filters:
      - filter_out: 0xFFFF

  # Max Temperature of the detecting pointers (Working status)
  # 暫時註釋掉，解決重複命令問題
  # - platform: modbus_controller
  #   modbus_controller_id: sp2500
  #   name: "Max Temperature"
  #   id: max_temperature
  #   icon: mdi:temperature-celsius
  #   register_type: holding
  #   address: 0xED
  #   value_type: S_WORD
  #   unit_of_measurement: "°C"
  #   timeout: 5s
  #   skip_updates: 3
  #   filters:
  #     - filter_out: 0x7FFF  # 過濾無效值
