esphome:
  name: sp2500
  friendly_name: sp2500

esp32:
  board: m5stack-atom
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG
# Enable Home Assistant API
api:

ota:
  - platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "Sp2500 Fallback Hotspot"
    password: "KUpOksaTIe2I"

captive_portal:

uart:
  rx_pin: 22
  tx_pin: 19
  baud_rate: 9600
  data_bits: 8
  stop_bits: 1

modbus:
  send_wait_time: 200ms
  id: modbus_pv

# Modbus 控制器 (從機地址 1)
modbus_controller:
  - id: sp2500
    address: 1  # 從機地址，預設 0x01
    modbus_id: modbus_pv
    update_interval: 5s  # 每 5 秒輪詢一次

sensor:
  # Grid voltage (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Grid Voltage"
    id: grid_voltage
    icon: mdi:sine-wave
    register_type: holding
    address: 0x00D1
    unit_of_measurement: "V"

  # AC output voltage (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Voltage"
    id: ac_output_voltage
    icon: mdi:sine-wave
    register_type: holding
    address: 0x00D8
    unit_of_measurement: "V"

  # AC output load percent (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Load Percent"
    id: ac_output_load_percent
    icon: mdi:percent
    register_type: holding
    address: 0x00DD
    unit_of_measurement: "%"

  # Battery capacity (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Battery Capacity"
    id: battery_capacity
    icon: mdi:battery-charging-high
    register_type: holding
    address: 0x00E2
    unit_of_measurement: "%"

  # Max Temperature of the detecting pointers (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Max Temperature"
    id: max_temperature
    icon: mdi:temperature-celsius
    register_type: holding
    address: 0x00ED
    value_type: S_WORD
    unit_of_measurement: "°C"