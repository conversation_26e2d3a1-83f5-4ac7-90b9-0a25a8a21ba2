substitutions:
  device_name: heater
  friendly_name: "暖風機"
  modbus_address: "1"
  update_interval: "5s"

esphome:
  name: ${device_name}
  friendly_name: ${friendly_name}
  build_path: build/${device_name}

esp32:
  board: m5stack-atom
  framework:
    type: arduino

# 優化日誌配置
logger:
  level: INFO  # 生產環境中使用INFO，開發時可改回DEBUG
  # baud_rate: 0  # 如果需要禁用串行日誌，取消註釋此行

# Enable Home Assistant API
api:

ota:
  - platform: esphome

web_server:

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "${friendly_name} Fallback Hotspot"
    password: "gzgCCrz6cDgI"

captive_portal:

# 優化UART配置
uart:
  rx_pin: 22
  tx_pin: 19
  baud_rate: 9600
  data_bits: 8
  stop_bits: 1
  rx_buffer_size: 512  # 增加緩衝區大小

modbus:
  send_wait_time: 200ms
  id: modbus1

# 優化Modbus控制器配置
modbus_controller:
  - id: heater
    address: ${modbus_address}  # 從機地址，預設 0x01
    modbus_id: modbus1
    update_interval: ${update_interval}  # 每 5 秒輪詢一次
    setup_priority: -10
    command_throttle: 100ms

select:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Operational Mode"
    id: operational_mode
    icon: "mdi:fan"
    address: 0x05
    value_type: U_WORD
    optimistic: true
    optionsmap:
      "停止": 0
      "暖風弱": 1
      "暖風強": 2
      "涼風弱": 3
      "涼風強": 4
      "換氣弱": 5
      "換氣強": 6
      "乾燥涼": 7
      "乾燥熱": 8
    entity_category: config

# 優化number組件
number:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Timer"
    id: "heater_timer"
    icon: mdi:clock
    register_type: holding
    address: 0x06
    value_type: U_WORD
    unit_of_measurement: min
    mode: box
    min_value: 30
    max_value: 540
    step: 30
    entity_category: config

# 優化傳感器配置
sensor:
  # 狀態暫存器 (表 2.2)
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Status Raw"
    id: heater_status_raw
    register_type: holding
    address: 0x30
    internal: true  # 隱藏原始值，使用模板傳感器顯示

  # 狀態解析 (表 2.2 暖風機功能設定狀態)
  - platform: template
    name: "Heater Status"
    id: heater_status
    icon: mdi:information-outline
    lambda: |-
      uint16_t status = id(heater_status_raw).state;

      // 檢查功能位元(0-7)是否都為0
      uint8_t function_bits = status & 0x00FF;
      if (function_bits == 0) return 0.0;  // 待機

      // 檢查特定功能
      uint8_t heat_mode = status & 0x03;        // 位元0-1: 暖房弱/強
      uint8_t cool_mode = (status >> 2) & 0x03; // 位元2-3: 涼風弱/強
      uint8_t vent_mode = (status >> 4) & 0x03; // 位元4-5: 換氣弱/強
      uint8_t dry_mode = (status >> 6) & 0x03;  // 位元6-7: 乾燥涼/熱

      // 返回更詳細的狀態代碼
      if (heat_mode == 1) return 10.0;  // 暖房弱
      if (heat_mode == 2) return 11.0;  // 暖房強
      if (cool_mode == 1) return 20.0;  // 涼風弱
      if (cool_mode == 2) return 21.0;  // 涼風強
      if (vent_mode == 1) return 30.0;  // 換氣弱
      if (vent_mode == 2) return 31.0;  // 換氣強
      if (dry_mode == 1) return 40.0;   // 乾燥涼
      if (dry_mode == 2) return 41.0;   // 乾燥熱

      return -1.0;  // 未知狀態
    state_class: measurement
  # 異常狀態 (表 2.3)
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Error Raw"
    id: heater_error_raw
    register_type: holding
    address: 0x31
    internal: true  # 隱藏原始值，使用模板傳感器顯示
    on_value:
      then:
        - if:
            condition:
              lambda: 'return id(heater_error_raw).state > 0;'
            then:
              - logger.log:
                  format: "Heater error detected: %d"
                  args: ['id(heater_error_raw).state']

  # 錯誤狀態解析
  - platform: template
    name: "Heater Error"
    id: heater_error
    icon: mdi:alert-circle-outline
    lambda: |-
      uint16_t error = id(heater_error_raw).state;
      return error;  // Return error code directly
    entity_category: diagnostic

  # 功能運行剩餘時間
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Remaining Time"
    id: Remaining_time_for_function_operation
    icon: mdi:timer
    register_type: holding
    address: 0x32
    value_type: U_WORD
    unit_of_measurement: "min"
    state_class: measurement

  # 暖風機各部件狀態 (表 2.5)
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Component Status"
    id: heater_component_status
    register_type: holding
    address: 0x34
    internal: true  # 隱藏原始值，使用二進制傳感器顯示

  # 室內溫度
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Temperature Indoor"
    id: Temperature_Indoor
    icon: mdi:temperature-celsius
    register_type: holding
    address: 0x35
    value_type: S_WORD
    unit_of_measurement: "°C"
    device_class: temperature
    state_class: measurement
    accuracy_decimals: 1
    filters:
      - filter_out: -32768  # Filter abnormal values

  # 風扇運轉時間
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Runtime"
    id: runtime
    register_type: holding
    address: 0x36
    unit_of_measurement: "H"
    device_class: duration
    state_class: total_increasing
    icon: mdi:clock-time-eight-outline

  # 濾網清潔
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Filter"
    id: filter
    register_type: holding
    address: 0x37
    internal: true  # Hide raw value, use text sensor to display

  - platform: wifi_signal
    name: "WiFi Signal"
    icon: mdi:wifi-strength-alert-outline
    update_interval: 60s
    entity_category: diagnostic
    state_class: measurement
# 優化二進制傳感器
binary_sensor:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Ventilation Valve"
    register_type: holding
    address: 0x34
    bitmask: 0x30 #(bit 4/5)
    device_class: opening
    filters:
      - delayed_on: 500ms
      - delayed_off: 500ms

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Night Light"
    register_type: holding
    address: 0x34
    bitmask: 0x100 #(bit 8)
    device_class: light
    filters:
      - delayed_on: 500ms
      - delayed_off: 500ms

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Motion"
    register_type: holding
    address: 0x34
    bitmask: 0x400 #(bit 10)
    device_class: motion
    filters:
      - delayed_on: 500ms
      - delayed_off: 500ms

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "heater"
    register_type: holding
    address: 0x34
    bitmask: 0x800 #(bit 11)
    device_class: heat
    filters:
      - delayed_on: 500ms
      - delayed_off: 500ms

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Fan"
    register_type: holding
    address: 0x34
    bitmask: 0x1000 #(bit 12)
    device_class: running
    filters:
      - delayed_on: 500ms
      - delayed_off: 500ms

# 優化開關配置
switch:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Auto-Vent Switch"
    id: Auto_Vent
    skip_updates: 5
    use_write_multiple: false
    register_type: holding
    address: 0x07
    bitmask: 1
    entity_category: config
    icon: "mdi:toggle-switch"
    device_class: switch
    on_turn_on:
      then:
        - logger.log: "Auto-Vent turned ON"
    on_turn_off:
      then:
        - logger.log: "Auto-Vent turned OFF"

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Motion Light Switch"
    id: motion_light
    skip_updates: 5
    use_write_multiple: false
    register_type: holding
    address: 0x08
    bitmask: 1
    entity_category: config
    icon: "mdi:toggle-switch"
    device_class: switch
    on_turn_on:
      then:
        - logger.log: "Motion Light turned ON"
    on_turn_off:
      then:
        - logger.log: "Motion Light turned OFF"
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Natural Wind"
    id: natural_wind
    skip_updates: 5
    use_write_multiple: false
    register_type: holding
    address: 0x0A
    bitmask: 1
    entity_category: config
    icon: "mdi:toggle-switch"
    device_class: switch
    on_turn_on:
      then:
        - logger.log: "Natural Wind turned ON"
    on_turn_off:
      then:
        - logger.log: "Natural Wind turned OFF"

text_sensor:
  - platform: wifi_info
    ip_address:
      name: "IP Address"
      icon: "mdi:ip-outline"
      entity_category: diagnostic

  - platform: template
    name: "Filter cleaning" # Filter cleaning notification
    icon: mdi:tooltip-check-outline
    entity_category: diagnostic
    lambda: |-
      if (id(filter).state == 0) {
        return {"不需清潔 "};
      } else if (id(filter).state == 1)  {
        return {"需清潔 "};
      } else {
        return {"偵測中"};
      }

  - platform: template
    name: "Heater Status Text"
    icon: mdi:information-outline
    entity_category: diagnostic
    lambda: |-
      uint16_t status = id(heater_status_raw).state;

      // 檢查功能位元(0-7)是否都為0
      uint8_t function_bits = status & 0x00FF;
      if (function_bits == 0) return {"待機中"};

      // 檢查功能位元
      uint8_t heat_mode = status & 0x03;        // 位元0-1: 暖房弱/強
      uint8_t cool_mode = (status >> 2) & 0x03; // 位元2-3: 涼風弱/強
      uint8_t vent_mode = (status >> 4) & 0x03; // 位元4-5: 換氣弱/強
      uint8_t dry_mode = (status >> 6) & 0x03;  // 位元6-7: 乾燥涼/熱

      // 檢查其他功能位元
      bool main_light = (status >> 8) & 0x01;    // 位元8: 主燈
      bool motion_light = (status >> 9) & 0x01;   // 位元9: 感應夜燈
      bool motion_wind = (status >> 10) & 0x01; // 位元10: 感應換氣
      bool lcd_alert = (status >> 11) & 0x01;  // 位元11: LCD線控省電
      bool natural_wind = (status >> 12) & 0x01;    // 位元12: 韻律風
      bool control_sound = (status >> 13) & 0x01;// 位元13: 中控提示音
      bool wired_control_sound = (status >> 14) & 0x01; // 位元14: 線控提示音
      bool force_control = (status >> 15) & 0x01;// 位元15: 強制中控模式

      // 構建狀態文本
      std::string result = "";

      // 檢查主要功能模式
      if (heat_mode == 1) result += "暖房弱";
      else if (heat_mode == 2) result += "暖房強";
      else if (cool_mode == 1) result += "涼風弱";
      else if (cool_mode == 2) result += "涼風強";
      else if (vent_mode == 1) result += "換氣弱";
      else if (vent_mode == 2) result += "換氣強";
      else if (dry_mode == 1) result += "乾燥涼";
      else if (dry_mode == 2) result += "乾燥熱";
      else if (function_bits > 0) result += "運行中";
      else result += "未知模式";

      // 添加其他功能狀態
      std::string extras = "";
      if (main_light) extras += ", 主燈開";
      if (motion_light) extras += ", 感應夜燈開";
      if (motion_wind) extras += ", 感應換氣開";
      if (lcd_alert) extras += ", LCD線控省電開";
      if (natural_wind) extras += ", 韻律風開";
      if (control_sound) extras += ", 中控提示音開";
      if (wired_control_sound) extras += ", 線控提示音開";
      if (force_control) extras += ", 強制中控開";

      if (!extras.empty()) {
        result += extras;
      }

      // 添加原始值（用於調試）
      char buffer[32];
      sprintf(buffer, " (0x%04X)", status);
      result += buffer;

      return {result};
    update_interval: 5s
