# HaCasa 主題配置
HaCasa:
  # 主要顏色
  primary-color: '#667eea'
  accent-color: '#764ba2'
  dark-primary-color: '#667eea'
  light-primary-color: '#f093fb'
  
  # 文字顏色
  primary-text-color: '#212121'
  text-primary-color: '#ffffff'
  secondary-text-color: '#757575'
  disabled-text-color: '#bdbdbd'
  label-badge-border-color: 'green'
  
  # 背景顏色
  primary-background-color: '#fafafa'
  secondary-background-color: '#e1e1e1'
  divider-color: 'rgba(0, 0, 0, .12)'
  table-row-background-color: '#ffffff'
  table-row-alternative-background-color: '#f5f5f5'
  
  # 導航
  paper-listbox-color: '#212121'
  paper-listbox-background-color: '#ffffff'
  paper-grey-50: '#fafafa'
  paper-grey-200: '#eeeeee'
  
  # 卡片
  paper-card-header-color: '#212121'
  paper-card-background-color: '#ffffff'
  paper-dialog-background-color: '#ffffff'
  paper-item-icon-color: '#44739e'
  paper-item-icon-active-color: '#ffc107'
  paper-item-icon_-_color: 'green'
  paper-item-selected_-_background-color: '#ffecb3'
  paper-tabs-selection-bar-color: 'green'
  
  # 標籤
  label-badge-red: '#ffc107'
  label-badge-text-color: '#212121'
  label-badge-background-color: '#f1c40f'
  
  # 滑塊
  paper-slider-knob-color: '#667eea'
  paper-slider-knob-start-color: '#667eea'
  paper-slider-pin-color: '#667eea'
  paper-slider-active-color: '#667eea'
  paper-slider-container-color: 'linear-gradient(var(--primary-background-color), var(--secondary-background-color)) no-repeat'
  paper-slider-secondary-color: '#33a02c'
  
  # 切換開關
  paper-toggle-button-checked-button-color: '#667eea'
  paper-toggle-button-checked-bar-color: '#667eea'
  paper-toggle-button-checked-ink-color: '#667eea'
  paper-toggle-button-unchecked-button-color: 'var(--disabled-text-color)'
  paper-toggle-button-unchecked-bar-color: 'var(--disabled-text-color)'
  paper-toggle-button-unchecked-ink-color: 'var(--disabled-text-color)'
  
  # 單選按鈕
  paper-radio-button-checked-color: '#667eea'
  paper-radio-button-checked-ink-color: 'var(--accent-color-name)'
  paper-radio-button-unchecked-color: 'var(--primary-text-color)'
  paper-radio-button-unchecked-ink-color: 'var(--primary-text-color)'
  
  # 複選框
  paper-checkbox-checked-color: '#667eea'
  paper-checkbox-checked-ink-color: '#667eea'
  paper-checkbox-unchecked-color: 'var(--primary-text-color)'
  paper-checkbox-unchecked-ink-color: 'var(--primary-text-color)'
  paper-checkbox-label-color: 'var(--primary-text-color)'
  
  # 輸入框
  paper-input-container-color: 'var(--secondary-text-color)'
  paper-input-container-focus-color: '#667eea'
  paper-input-container-invalid-color: 'var(--accent-color-name)'
  paper-input-container-input-color: 'var(--primary-text-color)'
  
  # 自定義變數
  ha-card-border-radius: '20px'
  ha-card-box-shadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
  
  # 字體
  primary-font-family: 'Montserrat, sans-serif'
  paper-font-common-base_-_font-family: 'Montserrat, sans-serif'
  paper-font-common-code_-_font-family: 'Montserrat, sans-serif'
  paper-font-body1_-_font-family: 'Montserrat, sans-serif'
  paper-font-subhead_-_font-family: 'Montserrat, sans-serif'
  paper-font-headline_-_font-family: 'Montserrat, sans-serif'
  paper-font-caption_-_font-family: 'Montserrat, sans-serif'
  paper-font-title_-_font-family: 'Montserrat, sans-serif'
  
  # 側邊欄
  sidebar-icon-color: '#667eea'
  sidebar-text-color: '#212121'
  sidebar-background-color: '#ffffff'
  sidebar-selected-background-color: 'rgba(102, 126, 234, 0.1)'
  sidebar-selected-icon-color: '#667eea'
  sidebar-selected-text-color: '#667eea'
  
  # 標題欄
  app-header-background-color: '#ffffff'
  app-header-text-color: '#212121'
  app-header-selection-bar-color: '#667eea'
  
  # 狀態顏色
  state-icon-color: '#44739e'
  state-icon-active-color: '#667eea'
  state-icon-unavailable-color: 'var(--disabled-text-color)'
