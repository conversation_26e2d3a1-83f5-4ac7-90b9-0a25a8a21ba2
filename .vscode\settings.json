{"mcpServers": {"mcp-installer": {"command": "npx", "args": ["-y", "@anaisbetts/mcp-installer"]}, "webresearch": {"command": "npx", "args": ["-y", "@mzxrai/mcp-webresearch@latest"]}, "Browser tools": {"command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp@latest"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "server-puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "excel": {"command": "npx", "args": ["-y", "@negokaz/excel-mcp-server"]}}}