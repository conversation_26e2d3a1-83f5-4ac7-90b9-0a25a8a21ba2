# HaCasa 房間卡片設置指南

## 1. 安裝準備

### 必要組件
- Home Assistant
- Button Card (通過HACS安裝)
- HaCasa主題文件

### 安裝步驟

1. **安裝Button Card**
   - 在HACS中搜索並安裝 "Button Card"
   - 重啟Home Assistant

2. **下載HaCasa主題**
   - 從 https://github.com/dam<PERSON><PERSON><PERSON>hoff/HaCasa 下載最新版本
   - 解壓到 `config/themes/` 目錄

3. **配置文件結構**
```
config/
├── themes/
│   └── HaCasa/
│       ├── hacasa.yaml
│       └── cards/
├── dashboard/
│   └── rooms.yaml
└── configuration.yaml
```

## 2. Configuration.yaml 配置

在 `configuration.yaml` 中添加以下配置：

```yaml
# 主題配置
frontend:
  themes: !include_dir_merge_named themes

# Lovelace配置
lovelace:
  mode: storage
  resources:
    - url: /hacsfiles/button-card/button-card.js
      type: module
    - url: "https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900"
      type: css
  dashboards:
    ha-casa-rooms:
      mode: yaml
      title: HaCasa Rooms
      icon: mdi:home
      show_in_sidebar: true
      filename: dashboard/rooms.yaml

# 模板傳感器範例
template:
  - sensor:
      - name: "Living Room Status"
        state: >
          {% if is_state('climate.living_room', 'heat') %}
            on
          {% else %}
            off
          {% endif %}
        
      - name: "Office Status"
        state: >
          {% if is_state('switch.office_heater', 'on') %}
            on
          {% else %}
            off
          {% endif %}
        
      - name: "Bathroom Status"
        state: >
          {% if is_state('climate.bathroom', 'heat') %}
            on
          {% else %}
            off
          {% endif %}
```

## 3. 實體定義範例

確保您有以下實體（根據實際情況調整）：

```yaml
# 溫度傳感器
sensor:
  - platform: template
    sensors:
      living_room_temperature:
        friendly_name: "Living Room Temperature"
        value_template: "23.96"
        unit_of_measurement: "°C"
      
      office_temperature:
        friendly_name: "Office Temperature"
        value_template: "24.48"
        unit_of_measurement: "°C"
      
      bathroom_temperature:
        friendly_name: "Bathroom Temperature"
        value_template: "23.78"
        unit_of_measurement: "°C"

# 開關和氣候控制
switch:
  - platform: template
    switches:
      living_room_heater:
        friendly_name: "Living Room Heater"
        turn_on:
          service: climate.set_hvac_mode
          target:
            entity_id: climate.living_room
          data:
            hvac_mode: heat
        turn_off:
          service: climate.set_hvac_mode
          target:
            entity_id: climate.living_room
          data:
            hvac_mode: "off"
```
