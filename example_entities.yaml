# 範例實體配置
# 將此內容添加到您的 configuration.yaml 或相關配置文件中

# 溫度傳感器
sensor:
  # Living Room 溫度
  - platform: template
    sensors:
      living_room_temperature:
        friendly_name: "客廳溫度"
        value_template: "23.96"
        unit_of_measurement: "°C"
        device_class: temperature
        
  # Office 溫度  
  - platform: template
    sensors:
      office_temperature:
        friendly_name: "辦公室溫度"
        value_template: "24.48"
        unit_of_measurement: "°C"
        device_class: temperature
        
  # Bathroom 溫度
  - platform: template
    sensors:
      bathroom_temperature:
        friendly_name: "浴室溫度"
        value_template: "23.78"
        unit_of_measurement: "°C"
        device_class: temperature

# 狀態傳感器
template:
  - sensor:
      # Living Room 狀態
      - name: "Living Room Status"
        state: >
          {% if is_state('climate.living_room', 'heat') or 
                is_state('switch.living_room_heater', 'on') %}
            on
          {% else %}
            off
          {% endif %}
        icon: >
          {% if is_state('sensor.living_room_status', 'on') %}
            mdi:fire
          {% else %}
            mdi:fire-off
          {% endif %}
        
      # Office 狀態
      - name: "Office Status"
        state: >
          {% if is_state('switch.office_heater', 'on') or 
                is_state('climate.office', 'heat') %}
            on
          {% else %}
            off
          {% endif %}
        icon: >
          {% if is_state('sensor.office_status', 'on') %}
            mdi:desktop-tower
          {% else %}
            mdi:desktop-tower-monitor
          {% endif %}
        
      # Bathroom 狀態
      - name: "Bathroom Status"
        state: >
          {% if is_state('climate.bathroom', 'heat') or 
                is_state('switch.bathroom_heater', 'on') %}
            on
          {% else %}
            off
          {% endif %}
        icon: >
          {% if is_state('sensor.bathroom_status', 'on') %}
            mdi:shower
          {% else %}
            mdi:shower-head
          {% endif %}

# 開關實體
switch:
  # Living Room 控制
  - platform: template
    switches:
      living_room_notification:
        friendly_name: "客廳通知"
        turn_on:
          service: notify.mobile_app
          data:
            message: "客廳通知已開啟"
        turn_off:
          service: notify.mobile_app
          data:
            message: "客廳通知已關閉"
        icon_template: mdi:bell
        
      living_room_schedule:
        friendly_name: "客廳排程"
        turn_on:
          service: automation.turn_on
          target:
            entity_id: automation.living_room_schedule
        turn_off:
          service: automation.turn_off
          target:
            entity_id: automation.living_room_schedule
        icon_template: mdi:microsoft-excel
        
      living_room_tablet:
        friendly_name: "客廳平板"
        turn_on:
          service: switch.turn_on
          target:
            entity_id: switch.living_room_tablet_power
        turn_off:
          service: switch.turn_off
          target:
            entity_id: switch.living_room_tablet_power
        icon_template: mdi:tablet
        
      living_room_fan:
        friendly_name: "客廳風扇"
        turn_on:
          service: fan.turn_on
          target:
            entity_id: fan.living_room
        turn_off:
          service: fan.turn_off
          target:
            entity_id: fan.living_room
        icon_template: mdi:fan

  # Office 控制
  - platform: template
    switches:
      office_ac:
        friendly_name: "辦公室空調"
        turn_on:
          service: climate.set_hvac_mode
          target:
            entity_id: climate.office
          data:
            hvac_mode: cool
        turn_off:
          service: climate.set_hvac_mode
          target:
            entity_id: climate.office
          data:
            hvac_mode: "off"
        icon_template: mdi:air-conditioner
        
      office_server:
        friendly_name: "辦公室伺服器"
        turn_on:
          service: switch.turn_on
          target:
            entity_id: switch.office_server_power
        turn_off:
          service: switch.turn_off
          target:
            entity_id: switch.office_server_power
        icon_template: mdi:harddisk
        
      office_tablet:
        friendly_name: "辦公室平板"
        turn_on:
          service: switch.turn_on
          target:
            entity_id: switch.office_tablet_power
        turn_off:
          service: switch.turn_off
          target:
            entity_id: switch.office_tablet_power
        icon_template: mdi:tablet
        
      office_fan:
        friendly_name: "辦公室風扇"
        turn_on:
          service: fan.turn_on
          target:
            entity_id: fan.office
        turn_off:
          service: fan.turn_off
          target:
            entity_id: fan.office
        icon_template: mdi:fan

  # Bathroom 控制
  - platform: template
    switches:
      bathroom_notification:
        friendly_name: "浴室通知"
        turn_on:
          service: notify.mobile_app
          data:
            message: "浴室通知已開啟"
        turn_off:
          service: notify.mobile_app
          data:
            message: "浴室通知已關閉"
        icon_template: mdi:bell
        
      bathroom_ventilation:
        friendly_name: "浴室通風"
        turn_on:
          service: fan.turn_on
          target:
            entity_id: fan.bathroom_exhaust
        turn_off:
          service: fan.turn_off
          target:
            entity_id: fan.bathroom_exhaust
        icon_template: mdi:air-conditioner
        
      bathroom_tablet:
        friendly_name: "浴室平板"
        turn_on:
          service: switch.turn_on
          target:
            entity_id: switch.bathroom_tablet_power
        turn_off:
          service: switch.turn_off
          target:
            entity_id: switch.bathroom_tablet_power
        icon_template: mdi:tablet

# 鎖具實體
lock:
  - platform: template
    name: bathroom_door
    value_template: "{{ is_state('input_boolean.bathroom_door_locked', 'on') }}"
    lock:
      service: input_boolean.turn_on
      target:
        entity_id: input_boolean.bathroom_door_locked
    unlock:
      service: input_boolean.turn_off
      target:
        entity_id: input_boolean.bathroom_door_locked

# 輸入布林值（用於模擬鎖具狀態）
input_boolean:
  bathroom_door_locked:
    name: 浴室門鎖
    initial: false
    icon: mdi:lock
