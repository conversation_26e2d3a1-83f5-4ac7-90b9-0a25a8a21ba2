esphome:
  name: softener-valve
  friendly_name: softener-valve
  on_boot:
    priority: -10
    then:
      - lambda: |-
          if (id(full_cm_number).has_state()) {
            id(full_cm) = id(full_cm_number).state;
          }
          if (id(empty_cm_number).has_state()) {
            id(empty_cm) = id(empty_cm_number).state;
          }

esp32:
  board: esp32-s3-devkitc-1
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG
#  baud_rate: 0
#  level: verbose

# Enable Home Assistant API
api:

ota:
  - platform: esphome

web_server:

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "softener-valve Fallback Hotspot"
    password: "agSAY9B0wPzk"

captive_portal:

uart:
  id: mod_uart
  tx_pin: 6
  rx_pin: 5
  baud_rate: 9600
  stop_bits: 1
  debug:

modbus:
  id: modbus1
  uart_id: mod_uart
  send_wait_time: 200ms

modbus_controller:
  - id: modbus_device
    modbus_id: modbus1
    address: 0x01
    command_throttle: 100ms
#   setup_priority: -10
    update_interval: 5s

i2c:
  sda: GPIO38
  scl: GPIO39
  scan: true

sensor:
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  - platform: pulse_counter
    pin: GPIO7
    name: "Water Speed"   #水流速度
    id: my_water
    unit_of_measurement: 'L/Min'
    update_interval: 10s
    device_class: water
    accuracy_decimals: 1
    state_class: total
    filters:
      - multiply: 0.00303030303030303  #一升水330???1/330=0.00303030303030303

  - platform: total_daily_energy
    name: "Daily Water use"   #每日用水量
    power_id: my_water
    unit_of_measurement: 'L'
    device_class: water
    accuracy_decimals: 1
    filters:
      - multiply: 60

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "softeners volume1"  # 剩餘水量/整數
    id: softeners_volume1
    register_type: holding
    address: 0x2002
    unit_of_measurement: "m³"
    device_class: water

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "softeners volume2"  # 剩餘水量/小數
    id: softeners_volume2
    register_type: holding
    address: 0x2003
    unit_of_measurement: "m³"
    device_class: water
    accuracy_decimals: 2
    filters:
      - multiply: 0.01

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "Softeners Expiry Date"  # 剩餘天數
    id: softeners_date
    icon: mdi:calendar-month
    register_type: holding
    address: 0x2004

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "Softeners Instantaneous Flow"  # 瞬時流量
    id: softeners_flow
    icon: mdi:pulse
    register_type: holding
    address: 0x2006
    unit_of_measurement: "L/Min"
    state_class: "measurement"
    device_class: water
    accuracy_decimals: 1
    filters:
      - multiply: 0.16666667

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "softeners time_h"  # 目前時間/時
    id: softeners_time_h
    register_type: holding
    address: 0x201D

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "softeners time_m"  # 目前時間/分
    id: softeners_time_m
    register_type: holding
    address: 0x201E

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "softeners remaintime_h"  # 再生時間/時
    id: softeners_remaintime_h
    register_type: holding
    address: 0x2009

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "softeners remaintime_m"  # 再生時間/分
    id: softeners_remaintime_m
    register_type: holding
    address: 0x200A

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "Softeners Fault State"  # 故障狀態
    id: softeners_fault_state
    register_type: holding
    address: 0x2005

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "Softeners State"  # 執行狀態
    id: softeners_state
    register_type: holding
    address: 0x2007

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "Softeners Signal"  # 信號輸出 B-01/B-02
    id: softeners_signal
    register_type: holding
    address: 0x200E

  - platform: template
    name: "Softeners Water Volume" #剩餘水量
    unit_of_measurement: "L"
    device_class: water
    state_class: total
    id: softeners_water_volume
    lambda: |-
      return (id(softeners_volume1).state * 1000 + id(softeners_volume2).state * 1000);

  - platform: vl53l0x
    id: distance_m
    address: 0x29
    update_interval: 60s
    long_range: false
    internal: true
  - platform: template
    unit_of_measurement: cm
    icon: mdi:arrow-expand-down
    name: distance
    id: distance
    update_interval: 60s
    lambda: |-
      return id(distance_m).state * 100;
  - platform: template
    name: "percentage"
    unit_of_measurement: '%'
    icon: mdi:percent
    lambda: |-
      if (id(distance).state < id(full_cm)) {
        return 100;
      }

      if (id(distance).state > id(empty_cm)) {
        return 0;
      }

      return 100 - (id(distance).state - id(full_cm))  / ((id(empty_cm) - id(full_cm)) / 100);
    update_interval: 15s

text_sensor:
  - platform: template
    name: "Softeners Time" #目前時間
    icon: "mdi:home-clock"
    id: softeners_time_hhmm
    lambda: |-
      int hour = id(softeners_time_h).state;
      int minute = id(softeners_time_m).state;
      char buffer[6];
      sprintf(buffer, "%02d:%02d", hour, minute);
      return std::string(buffer);

  - platform: template
    name: "Softeners Remain Time" #再生時間
    icon: "mdi:timer"
    id: ws_remain_time_hhmm
    lambda: |-
      int hour = id(softeners_remaintime_h).state;
      int minute = id(softeners_remaintime_m).state;
      char buffer[6];
      sprintf(buffer, "%02d:%02d", hour, minute);
      return std::string(buffer);

  - platform: template
    name: "Softeners Fault State" #故障狀態
    icon: mdi:tooltip-text-outline
    entity_category: diagnostic
    lambda: |-
      if (id(softeners_fault_state).state == 1) {
        return {"E1"};
      } else if (id(softeners_fault_state).state == 2)  {
        return {"E2"};
      } else if (id(softeners_fault_state).state == 3)  {
        return {"E3"};
      } else if (id(softeners_fault_state).state == 4)  {
        return {"E4"};
      } else {
        return {"正常"};
      }

  - platform: template
    name: "Softeners State Change" #執行狀態
    icon: mdi:tooltip-check-outline
    entity_category: diagnostic
    lambda: |-
      if (id(softeners_state).state == 1) {
        return {"運行"};
      } else if (id(softeners_state).state == 3)  {
        return {"反洗"};
      } else if (id(softeners_state).state == 4)  {
        return {"吸鹽"};
      } else if (id(softeners_state).state == 7)  {
        return {"正洗"};
      } else if (id(softeners_state).state == 8)  {
        return {"補水"};
      } else {
        return {"切換中"};
      }

time:
  - platform: homeassistant
    id: homeassistant_time

switch:
  - platform: template
    name: "Softeners Enforce"
    id: softeners_work
    optimistic: true
    turn_on_action:
      - lambda: |-
          std::vector<uint8_t> payload;
          payload.push_back(0x01);  // 設備地址
          payload.push_back(0x06);  // 功能碼：寫入單個保持寄存器
          payload.push_back(0x30);  // 寄存器地址高字節
          payload.push_back(0x18);  // 寄存器地址低字節
          payload.push_back(0x00);  // 數據高字節
          payload.push_back(0x01);  // 數據低字節
          id(modbus1).send_raw(payload);
      - delay: 500ms
      - switch.turn_off: softeners_work


select:
  - platform: template
    name: "Softeners Mode"
    id: softeners_mode
    optimistic: true
    options:
      - "A-01流量延滯型"
      - "A-02流量即時型"
    on_value:
      then:
        - lambda: |-
            uint16_t modbus_value = (x == "A-01流量延滯型") ? 0x0001 : 0x0002;
            std::vector<uint8_t> payload;
            payload.push_back(0x01);  // 設備地址
            payload.push_back(0x06);  // 功能碼：寫入單個保持寄存器
            payload.push_back(0x30);  // 寄存器地址高字節
            payload.push_back(0x02);  // 寄存器地址低字節
            payload.push_back((modbus_value >> 8) & 0xFF);  // 數據高字節
            payload.push_back(modbus_value & 0xFF);  // 數據低字節
            id(modbus1).send_raw(payload);

  - platform: modbus_controller
    modbus_controller_id: modbus_device
    name: "Signal Output"
    id: signal_output
    address: 0x200E
    value_type: U_WORD
    optimistic: true
    optionsmap:
      "B-01再生過程控制輸出": 1
      "B-02轉換過程控制輸出": 2

binary_sensor:
  - platform: status
    name: "Status"

globals:
  - id: full_cm
    type: float
    initial_value: '50'
  - id: empty_cm
    type: float
    initial_value: '100'

number:
  - platform: template
    name: "Full CM"
    id: full_cm_number
    optimistic: true
    restore_value: true
    min_value: 0
    max_value: 200
    step: 1
    mode: box
    initial_value: 50
    set_action:
      - globals.set:
          id: full_cm
          value: !lambda 'return x;'

  - platform: template
    name: "Empty CM"
    id: empty_cm_number
    optimistic: true
    restore_value: true
    min_value: 0
    max_value: 200
    step: 1
    mode: box
    initial_value: 100
    set_action:
      - globals.set:
          id: empty_cm
          value: !lambda 'return x;'