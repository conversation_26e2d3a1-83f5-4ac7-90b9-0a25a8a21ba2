# SP2500 ESPHome Configuration
# Enhanced with additional sensors and controls

substitutions:
  device_name: sp2500
  friendly_name: "SP2500"
  modbus_address: "1"
  update_interval: "5s"
  uart_rx_pin: "22"
  uart_tx_pin: "19"
  uart_baud_rate: "9600"

esphome:
  name: ${device_name}
  friendly_name: ${friendly_name}
  build_path: build/${device_name}

esp32:
  board: m5stack-atom
  framework:
    type: arduino

# Enable logging
logger:
  level: INFO  # 生產環境中使用INFO，開發時可改回DEBUG
  # baud_rate: 0  # 如果需要禁用串行日誌，取消註釋此行

# Enable Home Assistant API
api:

ota:
  - platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "${friendly_name} Fallback Hotspot"
    password: "KUpOksaTIe2I"

captive_portal:

uart:
  rx_pin: ${uart_rx_pin}
  tx_pin: ${uart_tx_pin}
  baud_rate: ${uart_baud_rate}
  data_bits: 8
  stop_bits: 1
  rx_buffer_size: 512  # 增加緩衝區大小

modbus:
  send_wait_time: 200ms
  id: modbus_pv

# Modbus 控制器 (從機地址 1)
modbus_controller:
  - id: sp2500
    address: ${modbus_address}  # 從機地址
    modbus_id: modbus_pv
    update_interval: ${update_interval}  # 每 5 秒輪詢一次
    command_throttle: 100ms
    setup_priority: -10
    on_online:
      then:
        - logger.log: "SP2500 controller back online!"
    on_offline:
      then:
        - logger.log: "SP2500 controller goes offline!"

sensor:
  # Grid voltage (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Grid Voltage"
    id: grid_voltage
    icon: mdi:sine-wave
    register_type: holding
    address: 0x00D1
    unit_of_measurement: "V"
    device_class: voltage
    state_class: measurement
    accuracy_decimals: 1

  # AC output voltage (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Voltage"
    id: ac_output_voltage
    icon: mdi:sine-wave
    register_type: holding
    address: 0x00D8
    unit_of_measurement: "V"
    device_class: voltage
    state_class: measurement
    accuracy_decimals: 1

  # AC output load percent (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Load Percent"
    id: ac_output_load_percent
    icon: mdi:percent
    register_type: holding
    address: 0x00DD
    unit_of_measurement: "%"
    state_class: measurement
    accuracy_decimals: 0

  # Battery capacity (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Battery Capacity"
    id: battery_capacity
    icon: mdi:battery-charging-high
    register_type: holding
    address: 0x00E2
    unit_of_measurement: "%"
    device_class: battery
    state_class: measurement
    accuracy_decimals: 0
    on_value_range:
      - below: 20
        then:
          - logger.log:
              format: "Low battery capacity: %.0f%%"
              args: ['id(battery_capacity).state']

  # Max Temperature of the detecting pointers (Working status)
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Max Temperature"
    id: max_temperature
    icon: mdi:temperature-celsius
    register_type: holding
    address: 0x00ED
    value_type: S_WORD
    unit_of_measurement: "°C"
    device_class: temperature
    state_class: measurement
    accuracy_decimals: 1
    on_value_range:
      - above: 70
        then:
          - logger.log:
              format: "High temperature: %.1f°C"
              args: ['id(max_temperature).state']

  # 新增的傳感器
  # Grid current
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Grid Current"
    id: grid_current
    icon: mdi:current-ac
    register_type: holding
    address: 0x00D2
    unit_of_measurement: "A"
    device_class: current
    state_class: measurement
    accuracy_decimals: 1

  # AC output current
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Current"
    id: ac_output_current
    icon: mdi:current-ac
    register_type: holding
    address: 0x00D9
    unit_of_measurement: "A"
    device_class: current
    state_class: measurement
    accuracy_decimals: 1

  # Grid frequency
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Grid Frequency"
    id: grid_frequency
    icon: mdi:sine-wave
    register_type: holding
    address: 0x00D3
    unit_of_measurement: "Hz"
    device_class: frequency
    state_class: measurement
    accuracy_decimals: 1

  # AC output frequency
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Frequency"
    id: ac_output_frequency
    icon: mdi:sine-wave
    register_type: holding
    address: 0x00DA
    unit_of_measurement: "Hz"
    device_class: frequency
    state_class: measurement
    accuracy_decimals: 1

  # Battery voltage
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Battery Voltage"
    id: battery_voltage
    icon: mdi:battery
    register_type: holding
    address: 0x00E0
    unit_of_measurement: "V"
    device_class: voltage
    state_class: measurement
    accuracy_decimals: 1

  # Battery current
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Battery Current"
    id: battery_current
    icon: mdi:current-dc
    register_type: holding
    address: 0x00E1
    unit_of_measurement: "A"
    device_class: current
    state_class: measurement
    accuracy_decimals: 1

  # PV input voltage
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "PV Input Voltage"
    id: pv_input_voltage
    icon: mdi:solar-power
    register_type: holding
    address: 0x00E3
    unit_of_measurement: "V"
    device_class: voltage
    state_class: measurement
    accuracy_decimals: 1

  # PV input current
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "PV Input Current"
    id: pv_input_current
    icon: mdi:solar-power
    register_type: holding
    address: 0x00E4
    unit_of_measurement: "A"
    device_class: current
    state_class: measurement
    accuracy_decimals: 1

  # PV input power
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "PV Input Power"
    id: pv_input_power
    icon: mdi:solar-power
    register_type: holding
    address: 0x00E5
    unit_of_measurement: "W"
    device_class: power
    state_class: measurement
    accuracy_decimals: 0

  # AC output active power
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Active Power"
    id: ac_output_active_power
    icon: mdi:power-plug
    register_type: holding
    address: 0x00DB
    unit_of_measurement: "W"
    device_class: power
    state_class: measurement
    accuracy_decimals: 0

  # AC output apparent power
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "AC Output Apparent Power"
    id: ac_output_apparent_power
    icon: mdi:power-plug
    register_type: holding
    address: 0x00DC
    unit_of_measurement: "VA"
    state_class: measurement
    accuracy_decimals: 0

  # Total energy production
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Total Energy Production"
    id: total_energy_production
    icon: mdi:counter
    register_type: holding
    address: 0x00F0
    unit_of_measurement: "kWh"
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 1

  # WiFi signal strength
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s
    entity_category: diagnostic

binary_sensor:
  # Inverter status
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Inverter Running"
    id: inverter_running
    register_type: holding
    address: 0x00D0
    bitmask: 0x01
    device_class: running

  # Grid connected
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Grid Connected"
    id: grid_connected
    register_type: holding
    address: 0x00D0
    bitmask: 0x02
    device_class: connectivity

  # Battery charging
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Battery Charging"
    id: battery_charging
    register_type: holding
    address: 0x00D0
    bitmask: 0x04
    device_class: battery_charging

  # Alarm status
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Alarm Status"
    id: alarm_status
    register_type: holding
    address: 0x00D0
    bitmask: 0x08
    device_class: problem

text_sensor:
  # Working mode
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Working Mode"
    id: working_mode
    register_type: holding
    address: 0x00D0
    bitmask: 0xF0
    lambda: |-
      uint8_t mode = (x >> 4) & 0x0F;
      switch (mode) {
        case 0: return "Standby";
        case 1: return "Grid Mode";
        case 2: return "Battery Mode";
        case 3: return "Fault Mode";
        case 4: return "Test Mode";
        default: return "Unknown";
      }

  # Error code
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Error Code"
    id: error_code
    register_type: holding
    address: 0x00EE
    lambda: |-
      uint16_t error = x;
      if (error == 0) {
        return "No Error";
      } else {
        char buffer[32];
        sprintf(buffer, "Error: 0x%04X", error);
        return buffer;
      }
    on_value:
      then:
        - if:
            condition:
              lambda: 'return id(error_code).state != "No Error";'
            then:
              - logger.log:
                  format: "Error detected: %s"
                  args: ['id(error_code).state.c_str()']

  # WiFi info
  - platform: wifi_info
    ip_address:
      name: "IP Address"
      icon: "mdi:ip-network"
      entity_category: diagnostic

switch:
  # Inverter on/off
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Inverter Power"
    id: inverter_power
    register_type: holding
    address: 0x0100
    bitmask: 0x01
    entity_category: config
    icon: "mdi:power"

  # Eco mode
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Eco Mode"
    id: eco_mode
    register_type: holding
    address: 0x0100
    bitmask: 0x02
    entity_category: config
    icon: "mdi:leaf"

select:
  # Output source priority
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Output Source Priority"
    id: output_source_priority
    address: 0x0101
    value_type: U_WORD
    optionsmap:
      "Utility First": 0
      "Solar First": 1
      "Battery First": 2
    entity_category: config
    icon: "mdi:priority-high"

  # Charger source priority
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Charger Source Priority"
    id: charger_source_priority
    address: 0x0102
    value_type: U_WORD
    optionsmap:
      "Utility First": 0
      "Solar First": 1
      "Solar and Utility": 2
      "Solar Only": 3
    entity_category: config
    icon: "mdi:battery-charging"

number:
  # Maximum charging current
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Maximum Charging Current"
    id: max_charging_current
    register_type: holding
    address: 0x0103
    value_type: U_WORD
    min_value: 10
    max_value: 60
    step: 5
    unit_of_measurement: "A"
    entity_category: config
    icon: "mdi:current-ac"

  # Maximum AC charging current
  - platform: modbus_controller
    modbus_controller_id: sp2500
    name: "Maximum AC Charging Current"
    id: max_ac_charging_current
    register_type: holding
    address: 0x0104
    value_type: U_WORD
    min_value: 2
    max_value: 30
    step: 2
    unit_of_measurement: "A"
    entity_category: config
    icon: "mdi:current-ac"
