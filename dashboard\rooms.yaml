# HaCasa 房間儀表板配置
title: Rooms
theme: HaCasa
path: rooms

# Button Card 模板定義
button_card_templates:
  # 房間卡片基礎模板
  room_card_base:
    type: custom:button-card
    size: 100%
    styles:
      card:
        - background: 'rgba(255, 255, 255, 0.1)'
        - border-radius: 20px
        - padding: 20px
        - backdrop-filter: blur(10px)
        - border: '1px solid rgba(255, 255, 255, 0.2)'
        - box-shadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
        - height: 180px
      name:
        - font-size: 24px
        - font-weight: 600
        - color: var(--primary-text-color)
        - margin-bottom: 8px
      state:
        - font-size: 16px
        - color: var(--secondary-text-color)
        - margin-bottom: 20px
    custom_fields:
      icon:
        card:
          type: custom:button-card
          icon: '[[[ return variables.room_icon ]]]'
          size: 40px
          styles:
            card:
              - background: '[[[ return variables.icon_color ]]]'
              - border-radius: 50%
              - width: 60px
              - height: 60px
              - position: absolute
              - top: 20px
              - right: 20px
            icon:
              - color: white
      temperature:
        card:
          type: custom:button-card
          entity: '[[[ return variables.temp_entity ]]]'
          show_name: false
          show_state: true
          styles:
            card:
              - background: none
              - border: none
              - box-shadow: none
              - position: absolute
              - top: 60px
              - left: 20px
            state:
              - font-size: 28px
              - font-weight: 700
              - color: var(--primary-text-color)
      status:
        card:
          type: custom:button-card
          entity: '[[[ return variables.status_entity ]]]'
          show_name: false
          show_state: true
          styles:
            card:
              - background: none
              - border: none
              - box-shadow: none
              - position: absolute
              - top: 95px
              - left: 20px
            state:
              - font-size: 16px
              - color: var(--secondary-text-color)
      controls:
        card:
          type: horizontal-stack
          cards:
            - type: custom:button-card
              icon: '[[[ return variables.control1_icon ]]]'
              size: 20px
              tap_action:
                action: toggle
                entity: '[[[ return variables.control1_entity ]]]'
              styles:
                card:
                  - background: 'rgba(255, 255, 255, 0.1)'
                  - border-radius: 12px
                  - width: 40px
                  - height: 40px
                  - border: none
                icon:
                  - color: var(--primary-text-color)
            - type: custom:button-card
              icon: '[[[ return variables.control2_icon ]]]'
              size: 20px
              tap_action:
                action: toggle
                entity: '[[[ return variables.control2_entity ]]]'
              styles:
                card:
                  - background: 'rgba(255, 255, 255, 0.1)'
                  - border-radius: 12px
                  - width: 40px
                  - height: 40px
                  - border: none
                icon:
                  - color: var(--primary-text-color)
            - type: custom:button-card
              icon: '[[[ return variables.control3_icon ]]]'
              size: 20px
              tap_action:
                action: toggle
                entity: '[[[ return variables.control3_entity ]]]'
              styles:
                card:
                  - background: 'rgba(255, 255, 255, 0.1)'
                  - border-radius: 12px
                  - width: 40px
                  - height: 40px
                  - border: none
                icon:
                  - color: var(--primary-text-color)
            - type: custom:button-card
              icon: '[[[ return variables.control4_icon ]]]'
              size: 20px
              tap_action:
                action: toggle
                entity: '[[[ return variables.control4_entity ]]]'
              styles:
                card:
                  - background: 'rgba(255, 255, 255, 0.1)'
                  - border-radius: 12px
                  - width: 40px
                  - height: 40px
                  - border: none
                icon:
                  - color: var(--primary-text-color)

# 主要視圖
views:
  - title: Rooms
    path: rooms
    type: masonry
    cards:
      # Living Room 卡片
      - type: custom:button-card
        template: room_card_base
        name: Living Room
        variables:
          room_icon: mdi:sofa
          icon_color: '#FF6B6B'
          temp_entity: sensor.living_room_temperature
          status_entity: sensor.living_room_status
          control1_icon: mdi:bell
          control1_entity: switch.living_room_notification
          control2_icon: mdi:microsoft-excel
          control2_entity: switch.living_room_schedule
          control3_icon: mdi:tablet
          control3_entity: switch.living_room_tablet
          control4_icon: mdi:fan
          control4_entity: switch.living_room_fan
        tap_action:
          action: navigate
          navigation_path: /lovelace/living-room
        styles:
          custom_fields:
            controls:
              - position: absolute
              - bottom: 20px
              - left: 20px
              - right: 20px

      # Office 卡片
      - type: custom:button-card
        template: room_card_base
        name: Office
        variables:
          room_icon: mdi:monitor
          icon_color: '#4ECDC4'
          temp_entity: sensor.office_temperature
          status_entity: sensor.office_status
          control1_icon: mdi:air-conditioner
          control1_entity: switch.office_ac
          control2_icon: mdi:harddisk
          control2_entity: switch.office_server
          control3_icon: mdi:tablet
          control3_entity: switch.office_tablet
          control4_icon: mdi:fan
          control4_entity: switch.office_fan
        tap_action:
          action: navigate
          navigation_path: /lovelace/office
        styles:
          custom_fields:
            controls:
              - position: absolute
              - bottom: 20px
              - left: 20px
              - right: 20px

      # Bathroom 卡片
      - type: custom:button-card
        template: room_card_base
        name: Bathroom
        variables:
          room_icon: mdi:shower
          icon_color: '#45B7D1'
          temp_entity: sensor.bathroom_temperature
          status_entity: sensor.bathroom_status
          control1_icon: mdi:bell
          control1_entity: switch.bathroom_notification
          control2_icon: mdi:air-conditioner
          control2_entity: switch.bathroom_ventilation
          control3_icon: mdi:tablet
          control3_entity: switch.bathroom_tablet
          control4_icon: mdi:lock
          control4_entity: lock.bathroom_door
        tap_action:
          action: navigate
          navigation_path: /lovelace/bathroom
        styles:
          custom_fields:
            controls:
              - position: absolute
              - bottom: 20px
              - left: 20px
              - right: 20px
