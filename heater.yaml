esphome:
  name: heater
  friendly_name: heater

esp32:
  board: m5stack-atom
  framework:
    type: arduino

# Enable logging
logger:
  level: DEBUG

# Enable Home Assistant API
api:

ota:
  - platform: esphome

web_server:

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "Heater Fallback Hotspot"
    password: "gzgCCrz6cDgI"

captive_portal:

uart:
  rx_pin: 22
  tx_pin: 19
  baud_rate: 9600
  data_bits: 8
  stop_bits: 1

modbus:
  send_wait_time: 200ms
  id: modbus1

# Modbus 控制器，針對暖風機 (從機地址 1)
modbus_controller:
  - id: heater
    address: 1  # 從機地址，預設 0x01
    modbus_id: modbus1
    update_interval: 5s  # 每 5 秒輪詢一次

select:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Operational Mode"
    id: operational_mode
    icon: "mdi:fan"
    address: 0x05
    value_type: U_WORD
    optimistic: true
    optionsmap:
      "停止": 0
      "暖風弱": 1
      "暖風強": 2
      "涼風弱": 3
      "涼風強": 4
      "換氣弱": 5
      "換氣強": 6
      "乾燥涼": 7
      "乾燥熱": 8

number:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Timer"
    id: "heater_timer"
    icon: mdi:clock
    register_type: holding
    address: 0x06
    value_type: U_WORD
    unit_of_measurement: min
    mode: box
    min_value: 30
    max_value: 540
    step: 30
#    lambda: "return  x * 1.0; "
#    write_lambda: |-
#      ESP_LOGD("main","Modbus Number incoming value = %f",x);
#      uint16_t b_capacity = x ;
#      payload.push_back(b_capacity);
#      return x * 1.0 ;
sensor:
  # 狀態暫存器 (表 2.2)
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Status Raw"
    id: heater_status_raw
    register_type: holding
    address: 0x30
  # 異常狀態 (表 2.3)
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Error Raw"
    id: heater_error_raw
    register_type: holding
    address: 0x31
  # 功能運行剩餘時間
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Remaining Time"
    id: Remaining_time_for_function_operation
    icon: mdi:timer
    register_type: holding
    address: 0x32
    value_type: U_WORD
    unit_of_measurement: "min"
  # 暖風機各部件狀態 (表 2.5)
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Component Status"
    id: heater_component_status
    register_type: holding
    address: 0x34
  # 室內溫度
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Temperature Indoor"
    id: Temperature_Indoor
    icon: mdi:temperature-celsius
    register_type: holding
    address: 0x35
    value_type: S_WORD
    unit_of_measurement: "°C"
  # 風扇運轉時間
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Runtime"
    id: runtime
    register_type: holding
    address: 0x36
    unit_of_measurement: "H"
  # 濾網清潔
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Filter"
    id: filter
    register_type: holding
    address: 0x37

  - platform: wifi_signal
    name: "WiFi Signal"
    icon: mdi:wifi-strength-alert-outline
    update_interval: 60s

binary_sensor:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Ventilation Valve"
    register_type: holding
    address: 0x34
    bitmask: 0x30 #(bit 4/5)

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Night Light"
    register_type: holding
    address: 0x34
    bitmask: 0x100 #(bit 8)

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Motion"
    register_type: holding
    address: 0x34
    bitmask: 0x400 #(bit 10)

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "heater"
    register_type: holding
    address: 0x34
    bitmask: 0x800 #(bit 11)

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Fan"
    register_type: holding
    address: 0x34
    bitmask: 0x1000 #(bit 12)

switch:
  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Auto-Vent Switch"
    id: Auto_Vent
    skip_updates: 5
    use_write_multiple: false
    register_type: holding
    address: 0x07
    bitmask: 1
    entity_category: config
    icon: "mdi:toggle-switch"

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Motion Light Switch"
    id: motion_light
    skip_updates: 5
    use_write_multiple: false
    register_type: holding
    address: 0x08
    bitmask: 1
    entity_category: config
    icon: "mdi:toggle-switch"

  - platform: modbus_controller
    modbus_controller_id: heater
    name: "Natural Wind"
    id: natural_wind
    skip_updates: 5
    use_write_multiple: false
    register_type: holding
    address: 0x0A
    bitmask: 1
    entity_category: config
    icon: "mdi:toggle-switch"

text_sensor:
  - platform: wifi_info
    ip_address:
      name: "IP Address"
      icon: "mdi:ip-outline"

  - platform: template
    name: "Filter cleaning" #濾網清潔通知
    icon: mdi:tooltip-check-outline
    entity_category: diagnostic
    lambda: |-
      if (id(filter).state == 0) {
        return {"不須清洗"};
      } else if (id(filter).state == 1)  {
        return {"需清潔 "};
      } else {
        return {"偵測中"};
      }